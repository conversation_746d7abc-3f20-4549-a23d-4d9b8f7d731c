import React, { useEffect, useRef } from "react";
import { <PERSON>R<PERSON>, School } from "lucide-react";
import { gsap } from "gsap";

// Asegúrate de importar tu ícono, por ejemplo:
// import { ArrowRight } from 'lucide-react';
// Asegúrate de que tu componente usa esta estructura exacta.
// El cambio más importante es el <div> exterior.

// import { ArrowRight } from 'lucide-react'; // O tu ícono
// Asegúrate de importar tu ícono
// import { ArrowRight } from 'lucide-react';

const BotonRosaVerde = () => {
  return (
    <div className="relative inline-flex group w-full sm:w-auto">
      {/* 1. LA CAPA DE BRILLO */}
      <div
        className="
          absolute -inset-0.5
          rounded-full
          
          /* --- ESTADO NORMAL: Brillo Rosa --- */
          bg-[conic-gradient(from_90deg_at_50%_50%,#ec4899_0%,#f472b6_50%,#ec4899_100%)]
          
          /* --- ESTADO HOVER: Brillo Verde --- */
          group-hover:bg-[conic-gradient(from_90deg_at_50%_50%,#16a34a_0%,#22c55e_50%,#16a34a_100%)]
          
          animate-spin-glow
          blur-xl
          transition-all duration-500 ease-in-out
          group-hover:blur-2xl // Mantenemos el desenfoque para dar intensidad
        "
        aria-hidden="true"
      />

      {/* 2. EL BOTÓN */}
      <button
        className="
          relative inline-flex items-center justify-center rounded-full
          font-semibold text-white text-base sm:text-lg w-full
          overflow-hidden
          transition-transform duration-300 ease-out group-hover:scale-105
          px-8 py-4
        "
      >
        {/* 3. La CAPA DE FONDO DEL BOTÓN */}
        <span
          className="
            absolute inset-0
            
            /* --- ESTADO NORMAL: Gradiente Rosa --- */
            bg-gradient-to-r from-pink-500 to-rose-600

            /* --- ESTADO HOVER: Gradiente Verde --- */
            group-hover:from-green-500 group-hover:to-teal-600
            
            opacity-80 backdrop-blur-md
            group-hover:opacity-100
            
            /* Transición suave para todos los cambios */
            transition-all duration-500 ease-in-out
          "
          aria-hidden="true"
        />

        {/* 4. La CAPA DE CONTENIDO (Sin cambios) */}
        <span className="relative flex items-center justify-center space-x-2">
          <span>Consulta Gratuita</span>
          <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
        </span>
      </button>
    </div>
  );
};

// --- TU SECCIÓN HERO CON EL NUEVO BOTÓN INTEGRADO ---
export default function HeroSection() {
  const aiTextRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (aiTextRef.current) {
      // Dividir el texto en letras individuales
      const text = "Inteligencia Artificial";
      const letters = text.split("").map((letter) => {
        if (letter === " ") {
          return `<span class="ai-letter" style="width: 0.5em;">&nbsp;</span>`;
        }
        return `<span class="ai-letter">${letter}</span>`;
      });

      aiTextRef.current.innerHTML = letters.join("");

      // Crear la animación de brillo
      const letterElements = aiTextRef.current.querySelectorAll('.ai-letter');

      // Timeline principal que se repite
      const mainTl = gsap.timeline({ repeat: -1, repeatDelay: 3 });

      // Primera animación: onda de brillo que se mueve a través de las letras
      const waveTl = gsap.timeline();
      waveTl.fromTo(letterElements, {
        filter: "brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0))",
        scale: 1
      }, {
        filter: "brightness(1.8) drop-shadow(0 0 25px rgba(255, 255, 255, 1))",
        scale: 1.05,
        duration: 0.15,
        stagger: 0.06,
        ease: "power2.out"
      })
      .to(letterElements, {
        filter: "brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0))",
        scale: 1,
        duration: 0.15,
        stagger: 0.06,
        ease: "power2.in"
      });

      // Segunda animación: pulso general
      const pulseTl = gsap.timeline();
      pulseTl.to(aiTextRef.current, {
        textShadow: "0 0 40px rgba(255, 255, 255, 1), 0 0 80px rgba(255, 255, 255, 0.6), 0 0 120px rgba(255, 255, 255, 0.3)",
        duration: 0.8,
        ease: "power2.inOut"
      })
      .to(aiTextRef.current, {
        textShadow: "0 0 1px rgba(0, 0, 0, 0), 0 0 40px rgb(255, 255, 255)",
        duration: 0.8,
        ease: "power2.inOut"
      });

      // Añadir ambas animaciones al timeline principal
      mainTl.add(waveTl, 0)
             .add(pulseTl, 0.5);

      // Animación inicial de entrada (solo una vez)
      gsap.fromTo(letterElements, {
        opacity: 0,
        y: 20
      }, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.05,
        ease: "back.out(1.7)",
        delay: 1
      });
    }
  }, []);

  return (
    <section className="relative min-h-screen bg-white text-blue-700 overflow-hidden flex items-center">
      {/* SVG Animated Background */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        <svg
          className="absolute inset-0 w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
            </filter>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="1" />
            </filter>
          </defs>

          {/* ... tu código SVG de burbujas permanece sin cambios ... */}
          <g filter="url(#blur)">
            {/* Burbuja 1 */}
            <circle r="12" fill="#0000ff" opacity="0.6">
              <animateMotion
                dur="15s"
                repeatCount="indefinite"
                path="M 10,110 Q 15,80 10,50 Q 5,20 10,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="15s"
                repeatCount="indefinite"
              />
            </circle>
            {/* ... más burbujas ... */}
            <circle r="18" fill="#0033ff" opacity="0.5">
              <animateMotion
                dur="20s"
                repeatCount="indefinite"
                begin="-5s"
                path="M 85,110 Q 80,80 85,50 Q 90,20 85,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="20s"
                repeatCount="indefinite"
                begin="-5s"
              />
            </circle>
            <circle r="10" fill="#0066ff" opacity="0.4">
              <animateMotion
                dur="12s"
                repeatCount="indefinite"
                begin="-2s"
                path="M 45,110 Q 50,80 45,50 Q 40,20 45,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-2s"
              />
            </circle>
            <circle r="22" fill="#1E40AF" opacity="0.3">
              <animateMotion
                dur="60s"
                repeatCount="indefinite"
                begin="-8s"
                path="M 25,110 Q 20,80 25,50 Q 30,20 25,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="60s"
                repeatCount="indefinite"
                begin="-8s"
              />
            </circle>
            <circle r="14" fill="#2563EB" opacity="0.4">
              <animateMotion
                dur="36s"
                repeatCount="indefinite"
                begin="-12s"
                path="M 70,110 Q 75,80 70,50 Q 65,20 70,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="18s"
                repeatCount="indefinite"
                begin="-12s"
              />
            </circle>
            <circle r="11" fill="#60A5FA" opacity="0.6">
              <animateMotion
                dur="28s"
                repeatCount="indefinite"
                begin="-3s"
                path="M 60,110 Q 55,80 60,50 Q 65,20 60,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="14s"
                repeatCount="indefinite"
                begin="-3s"
              />
            </circle>
          </g>
          <g filter="url(#glow)">
            <circle r="7" fill="#DBEAFE" opacity="0.4">
              <animateMotion
                dur="20s"
                repeatCount="indefinite"
                begin="-1s"
                path="M 35,110 Q 40,80 35,50 Q 30,20 35,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="20s"
                repeatCount="indefinite"
                begin="-1s"
              />
            </circle>
            <circle r="6" fill="#BFDBFE" opacity="0.5">
              <animateMotion
                dur="32s"
                repeatCount="indefinite"
                begin="-6s"
                path="M 80,110 Q 85,80 80,50 Q 75,20 80,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="32s"
                repeatCount="indefinite"
                begin="-6s"
              />
            </circle>
            <circle r="9" fill="#93C5FD" opacity="0.3">
              <animateMotion
                dur="44s"
                repeatCount="indefinite"
                begin="-10s"
                path="M 15,110 Q 10,80 15,50 Q 20,20 15,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="44s"
                repeatCount="indefinite"
                begin="-10s"
              />
            </circle>
            <circle r="8" fill="#1D4ED8" opacity="0.5">
              <animateMotion
                dur="26s"
                repeatCount="indefinite"
                begin="-4s"
                path="M 55,110 Q 60,80 55,50 Q 50,20 55,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="26s"
                repeatCount="indefinite"
                begin="-4s"
              />
            </circle>
            <circle r="5" fill="#60A5FA" opacity="0.6">
              <animateMotion
                dur="22s"
                repeatCount="indefinite"
                begin="-7s"
                path="M 90,110 Q 95,80 90,50 Q 85,20 90,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="22s"
                repeatCount="indefinite"
                begin="-7s"
              />
            </circle>
          </g>
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-32 pt-20 sm:pt-24">
        <div className="text-center animate-fade-in">
          <div className="inline-flex items-center space-x-2 bg-blue-600/10 backdrop-blur-sm px-4 py-2 rounded-full mb-8 border border-blue-600/20">
            <School className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">
              Expertos titulados en IA
            </span>
          </div>
          <h1
            className="
              text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl 
              font-bold mb-6 sm:mb-8 leading-tight 
              text-title-glow /* <-- Clase para el texto azul brillante */
              px-2 sm:px-0
            "
          >
            Transformamos tu Negocio con{" "}
            <span
              ref={aiTextRef}
              className="font-bold text-flawless-gradient text-shine-effect inline-block pb-4" /* <-- LA CLASE FINAL Y CORRECTA */
            >
              Inteligencia Artificial
            </span>
          </h1>
          <p
            className="text-base sm:text-lg md:text-xl text-blue-800/80 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed font-light px-4 sm:px-0"
            style={{
              textShadow:
                "0 0 15px rgba(255, 255, 255, 1), 0 0 10px rgba(255, 255, 255, 1), 0 0 5px rgba(255, 255, 255, 1), 0 2px 4px rgba(255, 255, 255, 1)",
            }}
          >
            Automatización inteligente, agentes de IA y soluciones
            personalizadas para impulsar tu empresa hacia el futuro
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0">
            <BotonRosaVerde />

            <button className="bg-white hover:bg-gray-100 text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 border border-blue-200 w-full sm:w-auto">
              Ver Casos de Éxito
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
